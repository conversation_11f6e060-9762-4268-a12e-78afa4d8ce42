---
description: when user mentions /debug, apply this rule
alwaysApply: false
---
## Rule to help debug errors

# Debug engine

Act as a quality expert to perform systematic debugging of issues within the code with larger context in mind

**Usage** - /debug [error description and context]

## Root Cause Analysis

Systematic Investigation:

<thinking>
- What are the possible underlying causes?
- What evidence supports each hypothesis?
- What would a proper fix look like that addresses the root cause?
</thinking>

**Root Cause Principles**

- Fix underlying problems, never just symptoms
- Understand why the problem occurred, not just what happened
- Implement prevention measures when possible
- Validate fix doesn't introduce new issues
- Identify systemic issues that could cause similar problems

## Systematic investigation

- Gather error messages, user actions, environment, recent changes or any related updates
- Understanding the chain of causation
- Identifying the minimal fix that addresses core issue
- Ensuring fix doesn't introduce new problems

## MCP servers (if available)

- Use playwright MCP or browser-tools MCP to collect console and network logs
- If the issue is frontend-related, take a screenshot of the app using playwright MCP

## Hypothesis Generation (3-5 possibilities)

- Hypothesis 1: [Potential root cause with supporting evidence]
- Hypothesis 2: [Alternative cause with different evidence pattern]
- Hypothesis 3: [Environmental/configuration cause with context]
- Hypothesis 4: [Integration/dependency cause with system analysis]

## Resolution proposal & approval

Provide below details to the user for resolution

- Root cause: [underlying problem identified]
- Solution: [approach taken and why]
- Prevention: [measures to avoid recurrence]

Implement the above on user confirmation and confirm;

- Notify the user once the fix is implemented
- Do NOT restart the frontend or backend server unless explicitly instructed
- User will confirm if the fix resolves the issue

## Additional Iterations (if needed)

- Refine solution based on testing and feedback
- Address any secondary issues discovered
- Improve error handling and user experience

## Code-Based Example Walkthrough

> **Scenario**: Application panics with `runtime error: invalid memory address or nil pointer dereference`.
>
> **Step-by-Step Debug Process:**
>
> 1. **Gather Evidence** – Capture stack trace, offending file & line.
> 2. **Reproduce** – Add a failing unit test.
> 3. **Hypothesis** – Nil `db` connection injected into service.
> 4. **Verify** – Insert guard clause and run test.
> 5. **Fix** – Ensure `NewService()` validates injected dependencies and returns error if nil.
>
> ```go
> // before
> func (s *UserService) Find(id string) (*User, error) {
>     return s.db.Find(id) // panic if s.db == nil
> }
>
> // after
> func (s *UserService) Find(id string) (*User, error) {
>     if s.db == nil {
>         return nil, fmt.Errorf("db is nil: %w", ErrInvalidState)
>     }
>     return s.db.Find(id)
> }
> ```
>
> **Prevention** – Add constructor validation & unit test to assert non-nil `db`.

- Refine solution based on testing and feedback
- Address any secondary issues discovered
- Improve error handling and user experience
